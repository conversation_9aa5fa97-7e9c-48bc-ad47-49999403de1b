import { NgFor } from '@angular/common';
import { Component } from '@angular/core';
import { mockDocumentTypes } from './mockData';
import { formatParentData } from './helpers';
import { DataService } from '../data.service';
import { sunburstAnnotationColors } from '../../constants/colors';

interface TableData {
  label: string;
  count: number;
  percent: number;
}

const headers = ['', 'Percent', 'Count'];

@Component({
  selector: 'venio-data-table-for-focused-section',
  standalone: true,
  imports: [NgFor],
  templateUrl: './data-table-for-focused-section.component.html',
  styleUrl: './data-table-for-focused-section.component.scss'
})
export class DataTableForFocusedSectionComponent {
  public headers = headers;
  public tableData: TableData[] = formatParentData(mockDocumentTypes);
  public chartColors: string[] = sunburstAnnotationColors;

  constructor(private dataService: DataService) { }

  ngOnInit() {
    this.dataService.data$.subscribe(data => {
      if (data) {
        this.tableData = data;
      }
    });
    this.dataService.isParentData$.subscribe(val => {
      // use val (true/false)
    });
  }

  getColorForIndex(index: number): string {
    return this.chartColors[index + 1] || this.chartColors[1];
  }
}
