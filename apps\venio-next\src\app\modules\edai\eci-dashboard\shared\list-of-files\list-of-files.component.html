<div class="t-relative t-overflow-x-auto t-p-4 t-border t-rounded-md t-border-gray-200 t-bg-white">
  <table class="t-min-w-full t-text-sm">
    <thead class="t-bg-gray-50 t-border-b t-border-gray-200">
      <tr>
        <th class="t-px-4 t-py-3 t-text-left t-font-semibold t-text-gray-700">Filename</th>
        <th class="t-px-4 t-py-3 t-text-left t-font-semibold t-text-gray-700">Summary</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let file of files; let i = index" class="t-border-b t-border-gray-100 hover:t-bg-gray-50">
        <td class="t-px-4 t-py-3 t-font-medium t-text-blue-600">{{ file.filename }}</td>
        <td class="t-px-4 t-py-3 t-text-gray-700">{{ file.summary }}</td>
      </tr>
    </tbody>
  </table>
</div>