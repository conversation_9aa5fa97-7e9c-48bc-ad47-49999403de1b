import { Component, OnInit } from '@angular/core';
import { DecimalPipe } from '@angular/common';
import { KENDO_BUTTONS } from "@progress/kendo-angular-buttons";
import { DataService } from '../data.service';

@Component({
  selector: 'venio-summary',
  standalone: true,
  imports: [KENDO_BUTTONS, DecimalPipe],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss'
})
export class SummaryComponent implements OnInit {
  public selectedDocuments = 0;
  public totalDocuments = 276897;

  constructor(private dataService: DataService) { }

  ngOnInit() {
    this.dataService.isFocusedSectionOpened$.subscribe(isOpened => {
      this.selectedDocuments = isOpened ? 13479 : 0;
    });
  }

  public onCaseButtonClick(): void {
    console.log('Case Info button clicked!');
  }
  public onNarrativeButtonClick(): void {
    console.log('But<PERSON> clicked!');
  }
}
