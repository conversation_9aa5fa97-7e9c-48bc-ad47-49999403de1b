<div class="t-bg-white t-rounded-lg t-shadow-md t-p-6 t-mb-6">
  <div class="t-flex t-justify-between t-items-center">
    <div class="t-flex t-gap-12">
      <div class="t-flex t-items-center t-gap-4">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" class="t-text-blue-600">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round" />
          <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
        </svg>
        <div class="t-flex t-flex-col">
          <div class="t-text-sm t-text-gray-600">Total Documents</div>
          <h2 class="t-text-3xl t-font-bold t-text-gray-900 t-m-0">{{ totalDocuments | number }}</h2>
        </div>
      </div>
      <div class="t-flex t-items-center t-gap-4">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" class="t-text-green-600">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round" />
          <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
          <path d="m9 12 2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
        </svg>
        <div class="t-flex t-flex-col">
          <div class="t-text-sm t-text-gray-600">Selected Documents</div>
          <h2 class="t-text-3xl t-font-bold t-text-gray-900 t-m-0">{{ selectedDocuments | number }}</h2>
        </div>
      </div>
    </div>

    <div class="t-flex t-gap-4">
      <button kendoButton size="large" (click)="onCaseButtonClick()" themeColor="primary"
        class="t-px-6 t-py-2 t-bg-purple-600 t-text-white t-border-purple-600">
        Case Info
      </button>
      <button kendoButton size="large" (click)="onNarrativeButtonClick()" themeColor="secondary"
        class="t-px-6 t-py-2 t-bg-gray-800 t-text-white t-border-gray-800">
        Narrative
      </button>
    </div>
  </div>
</div>