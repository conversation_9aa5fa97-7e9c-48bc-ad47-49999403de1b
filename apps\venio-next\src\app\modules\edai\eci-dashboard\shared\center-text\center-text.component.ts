import { Component, Input } from '@angular/core';
import { NgIf } from '@angular/common';
import { hyperlinkOpenSmIcon, SVGIcon } from '@progress/kendo-svg-icons';
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons';
import { DataService } from '../data.service';

@Component({
  selector: 'venio-center-text',
  standalone: true,
  imports: [KENDO_BUTTONS, NgIf],
  templateUrl: './center-text.component.html',
  styleUrl: './center-text.component.scss'
})
export class CenterTextComponent {
  @Input() centerText: string = 'Relevance';
  @Input() showViewDetails: boolean = true;

  constructor(private dataService: DataService) { }

  public svgOpenNew: SVGIcon = hyperlinkOpenSmIcon;
  openFocusedSection() {
    this.dataService.setIsFocusedSectionOpened(true);
  }
}
