<div class="t-w-full">
  <div id="content-filter-chart-container" class="t-bg-white t-border t-flex t-gap-6 t-p-7 t-rounded-3xl t-flex-col">
    <div class="t-flex t-justify-between t-items-center t-w-full">
      <div class="t-flex t-flex-col md:t-flex-row t-items-start md:t-items-center t-gap-0 md:t-gap-6">
        <h3 class="t-text-lg t-font-semibold t-text-gray-800 t-max-h-[36px] t-overflow-visible t-z-10">
          Content Filter
        </h3>
        <span class="t-text-sm t-text-purple-600 t-font-medium">View Details</span>
      </div>
      <div class="t-flex t-space-x-2 t-items-center">
        <button kendoButton title="Download" class="t-text-gray-600">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
          </svg>
        </button>
      </div>
    </div>
    <div class="t-w-full t-min-h-[300px]">
      <plotly-plot id="content-filter-chart" [data]="graph.data" [layout]="graph.layout"
        [config]="config"></plotly-plot>
    </div>
  </div>
</div>