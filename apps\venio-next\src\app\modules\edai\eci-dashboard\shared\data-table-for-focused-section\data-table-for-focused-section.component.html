<div class="t-relative t-overflow-x-auto t-p-4 t-border t-rounded-md t-border-gray-200 t-bg-white">
  <table class="t-w-full t-text-sm t-text-left t-text-gray-600">
    <thead class="t-bg-gray-50 t-border-b t-border-gray-200">
      <tr>
        <th *ngFor="let header of headers" class="t-p-3 t-text-left t-font-semibold t-text-gray-700">{{ header }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let row of tableData; let i = index" class="t-border-b t-border-gray-100 hover:t-bg-gray-50">
        <td class="t-p-3 t-flex t-items-center">
          <div class="t-h-3 t-w-3 t-rounded-sm t-mr-3" [style]="{ background: getColorForIndex(i) }"></div>
          <span class="t-font-medium t-text-gray-800">{{ row.label }}</span>
        </td>
        <td class="t-p-3 t-font-medium t-text-gray-700">{{ row.percent }}%</td>
        <td class="t-p-3 t-font-medium t-text-gray-700">{{ row.count | number }}</td>
      </tr>
    </tbody>
  </table>
</div>