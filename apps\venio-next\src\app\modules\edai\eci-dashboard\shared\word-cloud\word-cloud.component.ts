import { Component } from '@angular/core';
import { TagCloudComponent, CloudOptions, CloudData } from 'angular-tag-cloud-module';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';

@Component({
  selector: 'venio-word-cloud',
  standalone: true,
  imports: [TagCloudComponent, TitleAndDownloadComponent],
  templateUrl: './word-cloud.component.html',
  styleUrl: './word-cloud.component.scss'
})
export class WordCloudComponent {
  options: CloudOptions = {
    // if width is between 0 and 1 it will be set to the width of the upper element multiplied by the value
    width: 1000,
    // if height is between 0 and 1 it will be set to the height of the upper element multiplied by the value
    height: 400,
    overflow: false,
  };

  data: CloudData[] = [
    { text: 'adoption', weight: 15, color: '#6366f1', tooltip: 'Adoption proceedings' },
    { text: 'kearney', weight: 14, color: '#8b5cf6', tooltip: 'Kearney case' },
    { text: 'foster', weight: 13, color: '#a855f7', tooltip: 'Foster care' },
    { text: 'regier', weight: 12, color: '#c084fc', tooltip: 'Regier family' },
    { text: 'dependency', weight: 11, color: '#ddd6fe', tooltip: 'Dependency matters' },
    { text: 'brian', weight: 10, color: '#f3e8ff', tooltip: 'Brian case' },
    { text: 'visitation', weight: 10, color: '#6366f1', tooltip: 'Visitation rights' },
    { text: 'macdonald', weight: 9, color: '#8b5cf6', tooltip: 'MacDonald case' },
    { text: 'heather', weight: 9, color: '#a855f7', tooltip: 'Heather case' },
    { text: 'dci', weight: 8, color: '#c084fc', tooltip: 'DCI involvement' },
    { text: 'tamara', weight: 8, color: '#ddd6fe', tooltip: 'Tamara case' },
    { text: 'gal', weight: 7, color: '#f3e8ff', tooltip: 'Guardian ad litem' },
    { text: 'stefani', weight: 7, color: '#6366f1', tooltip: 'Stefani case' },
    { text: 'paternal', weight: 6, color: '#8b5cf6', tooltip: 'Paternal rights' },
    { text: 'maternal', weight: 6, color: '#a855f7', tooltip: 'Maternal rights' },
    { text: 'custody', weight: 6, color: '#c084fc', tooltip: 'Custody arrangements' },
    { text: 'placement', weight: 5, color: '#ddd6fe', tooltip: 'Child placement' },
    { text: 'termination', weight: 5, color: '#f3e8ff', tooltip: 'Parental rights termination' },
    { text: 'reunification', weight: 4, color: '#6366f1', tooltip: 'Family reunification' },
    { text: 'permanency', weight: 4, color: '#8b5cf6', tooltip: 'Permanency planning' },
    { text: 'guardian', weight: 4, color: '#a855f7', tooltip: 'Legal guardian' },
    { text: 'court', weight: 4, color: '#c084fc', tooltip: 'Court proceedings' },
    { text: 'hearing', weight: 3, color: '#ddd6fe', tooltip: 'Court hearing' },
    { text: 'petition', weight: 3, color: '#f3e8ff', tooltip: 'Legal petition' },
    { text: 'welfare', weight: 3, color: '#6366f1', tooltip: 'Child welfare' },
    { text: 'services', weight: 3, color: '#8b5cf6', tooltip: 'Social services' },
    { text: 'assessment', weight: 2, color: '#a855f7', tooltip: 'Case assessment' },
    { text: 'investigation', weight: 2, color: '#c084fc', tooltip: 'Investigation' },
    { text: 'report', weight: 2, color: '#ddd6fe', tooltip: 'Case report' },
    { text: 'recommendation', weight: 2, color: '#f3e8ff', tooltip: 'Court recommendation' }
  ];
}
